-- MySQL数据库初始化脚本
-- 请以root用户身份执行此脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS taiherenyi 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'taiherenyi'@'localhost' IDENTIFIED BY 'rXBGp54HzcYewSpe';

-- 授权
GRANT ALL PRIVILEGES ON taiherenyi.* TO 'taiherenyi'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 使用数据库
USE taiherenyi;

-- 显示创建结果
SELECT 'Database and user created successfully!' AS result;
