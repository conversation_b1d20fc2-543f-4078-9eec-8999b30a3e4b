package com.zsm.task;

import cn.hutool.core.date.DateUtil;
import com.zsm.service.DispensingService;
import com.zsm.service.HangChuangService;
import com.zsm.service.Nhsa3505Service;
import com.zsm.superset.TraceabilityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 太和人医-定时任务类
 *
 * <AUTHOR>
 * @date 2025/5/29 下午10:45
 */
@Component
@Slf4j
public class TaiHeRenYiTask {

    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private TraceabilityService traceabilityService;
    @Resource
    private HangChuangService hangChuangService;
    @Resource
    private DispensingService dispensingService;
    /**
     * 同步发药时间，每5分钟执行一次
     * 目的:根据扫码任务去关联发药明细的业务数据,重新查询his接口复写`发药时间`
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void syncDispenseTimeTask() {
        log.info("ShuChengTask：开始同步发药时间");
        try {
            // 调用Service实现发药时间同步逻辑
            nhsa3505Service.syncTaiHeDispenseTime();
            log.info("ShuChengTask：发药时间同步完成");
        } catch (Exception e) {
            log.info("ShuChengTask：发药时间同步失败", e);
            e.printStackTrace();
        }
    }

    /**
     * 上传3505数据到最终平台,每3小时执行一次(早上7点到夜里23点)
     */
    @Scheduled(cron = "0 0 7-23/3 * * ?")
    public void uploadDataToPlatformTask() {
        try {
            log.info("执行定时任务-上传3505数据到最终平台");
            nhsa3505Service.uploadDataToPlatform();
            log.info("定时任务执行完成-上传3505数据到最终平台");
        } catch (Exception e) {
            log.error("上传3505数据到最终平台失败", e);
            e.printStackTrace();
        }
    }

    /**
     * 生成追溯码数据，上传superSet平台，每2小时执行一次
     */
    @Scheduled(cron = "0 0 */2 * * ?")
    public void uploadTraceabilityDataToSuperSetTask() {
        try {
            log.info("执行定时任务-生成追溯码数据并上传superSet平台");
            // 调用Service实现追溯码数据生成和上传逻辑
            traceabilityService.generateAndUploadToSuperSet();
            log.info("定时任务执行完成-生成追溯码数据并上传superSet平台");
        } catch (Exception e) {
            log.error("生成追溯码数据并上传superSet平台失败", e);
            e.printStackTrace();
        }
    }


    /**
     * 同步HIS药品字典，每天下午1点执行一次
     */
    @Scheduled(cron = "0 0 13 * * ?")
    public void syncHisDrugDictionaryTask() {
        try {
            log.info("执行定时任务-同步HIS药品字典");
            // 调用HangChuangService实现药品字典同步逻辑
            hangChuangService.syncDrugDictionary();
            log.info("定时任务执行完成-同步HIS药品字典");
        } catch (Exception e) {
            log.error("同步HIS药品字典失败", e);
            e.printStackTrace();
        }
    }

    /**
     * 住院药房拆零确认定时任务，12点半,下午6点半,夜里11点半,分别执行一次
     * 根据执行时间计算相应的数据处理时间范围：
     * - 12:30执行时，处理0:00-12:30的数据
     * - 18:30执行时，处理12:30-18:30的数据
     * - 23:30执行时，处理00:30-23:30的数据
     */
    @Scheduled(cron = "0 30 12,18,23 * * ?")
    public void processInpatientDispenseTask() {
        log.info("FuYangRenYiTask：开始执行住院药房拆零确认任务");
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime;
            LocalDateTime endTime;
            
            // 根据当前时间确定处理的时间范围
            int currentHour = now.getHour();
            LocalDateTime today = now.toLocalDate().atStartOfDay();
            
            if (currentHour == 12) {
                // 12:30执行，处理0:00-12:30的数据
                startTime = today;
                endTime = today.with(LocalTime.of(12, 30));
                log.info("处理时间范围：{} 至 {}", startTime, endTime);
            } else if (currentHour == 18) {
                // 18:30执行，处理12:30-18:30的数据
                startTime = today.with(LocalTime.of(12, 30));
                endTime = today.with(LocalTime.of(18, 30));
                log.info("处理时间范围：{} 至 {}", startTime, endTime);
            } else if (currentHour == 23) {
                // 23:30执行，处理00:00-23:30的数据
                startTime = today;
                endTime = today.with(LocalTime.of(23, 30));
                log.info("处理时间范围：{} 至 {}", startTime, endTime);
            } else {
                log.warn("非预期的执行时间点：{}，跳过任务执行", currentHour);
                return;
            }
            
            // 调用按时间范围处理的方法
            dispensingService.processInpatientDispenseConfirmationByTimeRange(startTime, endTime);
            
        } catch (Exception e) {
            log.error("FuYangRenYiTask：住院药房拆零确认任务执行失败", e);
            e.printStackTrace();
        }
    }


    /**
     * 住院患者追溯码补录定时任务，每3小时执行一次（早上11点到夜里23点）
     * 按照上周日到本周六的时间范围处理，提升住院采集率
     */
    @Scheduled(cron = "0 0 11-23/3 * * ?")
    public void processWeeklyInpatientTraceabilityTask() {
        log.info("开始执行住院患者追溯码补录任务");
        try {

            final String today = DateUtil.today();
        
            // 调用查询今日出院患者的方法
            hangChuangService.processWeeklyInpatientTraceability(today, today);
            
            log.info("查询今日出院患者信息任务执行完成");
            
        } catch (Exception e) {
            log.error("住院患者追溯码补录任务执行失败", e);
            e.printStackTrace();
        }
    }
}
