server:
  port: 1168
  servlet:
    context-path: /api

spring:
  application:
    name: taiHeRenYi
  
  # 环境配置
  profiles:
    active: test  # 可选值: dev, test, prod
  
  # 启用虚拟线程
  threads:
    virtual:
      enabled: true
  
  # 多数据源配置
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组，默认值即为master
      primary: master
      # 严格匹配数据源，默认false. true未匹配到指定数据源时抛异常，false使用默认数据源
      strict: false
      # 全局 Druid 配置
      druid:
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connection-properties:
          druid.stat.mergeSql: true
          druid.stat.slowSqlMillis: 5000
        # 配置DruidStatFilter
        web-stat-filter:
          enabled: true
          url-pattern: "/*"
          exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
        # 配置DruidStatViewServlet
        stat-view-servlet:
          enabled: true
          url-pattern: "/druid/*"
          # IP白名单(没有配置或者为空，则允许所有访问)
          allow: 127.0.0.1,*************
          # IP黑名单 (存在共同时，deny优先于allow)
          deny: ************
          # 禁用HTML页面上的"Reset All"功能
          reset-enable: false
          # 登录名
          login-username: admin
          # 登录密码
          login-password: 123456
      # datasource:
      #   # 主数据源
      #   master:
      #     type: com.alibaba.druid.pool.DruidDataSource
      #     driver-class-name: com.mysql.cj.jdbc.Driver
      #     url: ****************************************************************************************************************************************************
      #     username: demo_mbp
      #     password: hPeBF8EXB7PzaaGX
      #     # Druid 连接池配置（只保留连接池相关配置）
      #     druid:
      #       # 初始连接数
      #       initial-size: 5
      #       # 最小连接池数量
      #       min-idle: 10
      #       # 最大连接池数量
      #       max-active: 20
      #       # 配置获取连接等待超时的时间
      #       max-wait: 60000
      #       # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      #       time-between-eviction-runs-millis: 60000
      #       # 配置一个连接在池中最小生存的时间，单位是毫秒
      #       min-evictable-idle-time-millis: 300000
      #       # 配置一个连接在池中最大生存的时间，单位是毫秒
      #       max-evictable-idle-time-millis: 900000
      #       # 配置检测连接是否有效
      #       validation-query: SELECT 1 FROM DUAL
      #       test-while-idle: true
      #       test-on-borrow: false
      #       test-on-return: false
      #       # 打开PSCache，并且指定每个连接上PSCache的大小
      #       pool-prepared-statements: true
      #       max-pool-prepared-statement-per-connection-size: 20
        # 从数据源1 - 读库
        # slave_1:
        #   type: com.alibaba.druid.pool.DruidDataSource
        #   driver-class-name: com.mysql.cj.jdbc.Driver
        #   url: *********************************************************************************************************************************************
        #   username: root
        #   password: 123456
        #   druid:
        #     initial-size: 5
        #     min-idle: 5
        #     max-active: 10
        #     max-wait: 60000
        #     time-between-eviction-runs-millis: 60000
        #     min-evictable-idle-time-millis: 300000
        #     max-evictable-idle-time-millis: 900000
        #     validation-query: SELECT 1 FROM DUAL
        #     test-while-idle: true
        #     test-on-borrow: false
        #     test-on-return: false

        # 从数据源2 - 读库
        # slave_2:
        #   type: com.alibaba.druid.pool.DruidDataSource
        #   driver-class-name: com.mysql.cj.jdbc.Driver
        #   url: *********************************************************************************************************************************************
        #   username: root
        #   password: 123456
        #   druid:
        #     initial-size: 5
        #     min-idle: 5
        #     max-active: 10
        #     max-wait: 60000
        #     time-between-eviction-runs-millis: 60000
        #     min-evictable-idle-time-millis: 300000
        #     max-evictable-idle-time-millis: 900000
        #     validation-query: SELECT 1 FROM DUAL
        #     test-while-idle: true
        #     test-on-borrow: false
        #     test-on-return: false
# MyBatis Plus 配置
mybatis-plus:
  # 配置扫描通用枚举
  type-enums-package: com.zsm.enums
  # 配置 Mapper XML 映射文件路径
  mapper-locations: classpath*:/mapper/**/*.xml
  # 配置 MyBatis 数据返回类型别名（默认别名是类名）
  type-aliases-package: com.zsm.entity
  
  # MyBatis 原生配置
  configuration:
    # 自动驼峰命名规则（camel case）映射
    map-underscore-to-camel-case: true
    # 当查询结果为空时字段返回为 null，不返回空串
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

  # 全局配置
  global-config:
    # 关闭 MyBatis Plus Banner
    banner: false
    # 数据库相关配置
    db-config:
      # 主键类型（AUTO:"数据库ID自增" INPUT:"用户输入ID" ASSIGN_ID:"雪花算法ID" ASSIGN_UUID:"UUID"）
      id-type: AUTO
      # 插入策略（NOT_NULL:"非 NULL 判断" NOT_EMPTY:"非空判断" DEFAULT:"默认" NEVER:"不加入 SQL"）
      insert-strategy: NOT_EMPTY
      # 更新策略（NOT_NULL:"非 NULL 判断" NOT_EMPTY:"非空判断" DEFAULT:"默认" NEVER:"不加入 SQL"）
      update-strategy: NOT_EMPTY
      # 数据库大写下划线转换
      table-underline: true
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置 - 日志格式和输出已在 logback-spring.xml 中配置
logging:
  level:
    # 以下配置会与 logback-spring.xml 中的配置合并
    # XML 中已有相同配置，这里可以作为备用或覆盖特定包的日志级别
    com.zsm.mapper: debug
    com.baomidou.dynamic.datasource: debug
    druid.sql.Statement: debug

# SpringDoc OpenAPI 3 配置 (Swagger)
springdoc:
  # API 文档基本信息配置
  api-docs:
    # 是否启用 OpenAPI 3
    enabled: true
    # API 文档路径，默认为 /v3/api-docs
    path: /v3/api-docs
  
  # Swagger UI 配置
  swagger-ui:
    # 是否启用 Swagger UI
    enabled: true
    # Swagger UI 访问路径，默认为 /swagger-ui.html
    path: /swagger-ui.html
    # 设置请求的基础路径
    operationsSorter: alpha
    # 标签排序
    tagsSorter: alpha
    # 尝试它功能
    tryItOutEnabled: true
    # 显示请求头
    displayRequestDuration: true
  
  # 分组配置
  group-configs:
    - group: 'default'
      display-name: '默认接口'
      paths-to-match: '/**'
  
  # 全局安全配置
  show-actuator: false

